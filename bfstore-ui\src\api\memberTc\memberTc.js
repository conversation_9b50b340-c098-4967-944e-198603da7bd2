import request from '@/utils/request'
// 查询培训卡列表
export function listCardTc(query) {
    return request({
        url: '/system/cardTc/listPage',
        method: 'get',
        params: query
    })
}

// 查询培训卡列表(不分页)
export function listAllCardTc(query) {
    return request({
        url: '/system/cardTc/listAll',
        method: 'get',
        params: query
    })
}

// 查询培训会员列表
export function listMemberTc(query) {
    return request({
        url: '/system/memberTc/list',
        method: 'get',
        params: query
    })
}
// 查询培训会员列表
export function getMemberTc() {
    return request({
        url: '/system/cardTc/listPage',
        method: 'get',
        params: query
    })
}
// 加入公海
export function removeRelationTc(id) {
    return request({
        url: '/system/memberTc/removeRelation/' + id,
        method: 'delete',
    })
}
//培训卡附加订单新增
export function additionalTc(data) {
    return request({
        url: '/system/additionalTc',
        method: 'post',
        data: data
    })
}
//支付附加订单
export function payTcadditional(data) {
  return request({
    url: '/system/additionalTc/payTc',
    method: 'put',
    data: data
  })
}
//撤销附加订单
export function cancelAdditionalTc(data) {
    return request({
        url: '/system/additionalTc/cancel',
        method: 'post',
        data: data
    })
}

//获取附加订单实付金额
export function getAmountTc(params) {
    return request({
        url: '/system/additionalTc/getAmount',
        method: 'get',
        params,
    })
}
//查询附加订单列表
export function getAdditionalTcList(params) {
    return request({
        url: '/system/additionalTc/list',
        method: 'get',
        params,
    })
}

//查询附加订单详情
export function getAdditionalTcDetail(id) {
    return request({
        url: '/system/additionalTc/'+id,
        method: 'get',
    })
}

//查询分配上课教练列表
export function getCardTcCoachList(params) {
    return request({
        url: '/system/cardTc/listCoachAssign',
        method: 'get',
        params,
    })
}

//查询未分配上课教练列表
export function listTcCoachUnassign(params) {
    return request({
        url: '/system/cardTc/listCoachUnassign',
        method: 'get',
        params,
    })
}

//分配上课教练
export function assignTcCoach(data) {
    return request({
        url: '/system/cardTc/assignCoach',
        method: 'post',
        data: data
    })
}
//移除上课教练
export function removeTcCoach(data) {
    return request({
        url: '/system/cardTc/removeCoach',
        method: 'post',
        data: data
    })
}

// 逻辑删除培训卡
export function removeCardTc(id) {
    return request({
        url: '/system/cardTc/' + id,
        method: 'delete',
    })
}
//设置主教练
export function setTcMainCoach(cardCId,coachId) {
    return request({
        url: `/system/cardTc/setMainCoach/${cardCId}/${coachId}`,
        method: 'put',
    })
}

//分页查询请假记录
export function listLeaveRecordsTc(query) {
  return request({
    url: '/system/cardTc/listLeaveRecords',
    method: 'get',
    params: query
  })
}

// 批量取消请假记录
export function removeLeaveRecordsTc(ids) {
  return request({
    url: '/system/cardTc/removeLeaveRecords/' + ids,
    method: 'delete',
  })
}