package com.bf.card.controller;

import com.bf.card.domain.CardC;
import com.bf.card.service.ICardCService;
import com.bf.common.core.web.controller.BaseController;
import com.bf.common.core.web.page.TableDataInfo;
import com.bf.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 卡_课程Controller
 *
 * <AUTHOR>
 * @date 2024-7-29
 */
@Api(value = "课程卡", tags = "课程卡")
@RestController
@RequestMapping("/cardC")
public class CardCController extends BaseController
{
    
    @Autowired
    private ICardCService cardCService;
    
    /**
     * 获取可用卡
     */
    @ApiOperation("获取可用卡")
    @RequiresPermissions("card:memberC:list")
    @GetMapping("/listAvailable")
    public TableDataInfo listAvailableCard(CardC cardC)
    {
        startPage();
        List<CardC> cardCList = cardCService.listAvailableCard(cardC);
        return getDataTable(cardCList);
    }
}
