package com.bf.card.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bf.card.domain.CardC;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 卡_课程卡Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Mapper
 public interface CardCMapper extends BaseMapper<CardC>
{
    /**
     * 查询卡_课程卡
     *
     * @param cardC 卡_课程卡主键
     * @return 卡_课程卡
     */
     CardC selectCardCById(CardC cardC);


    /**
     * 修改卡_课程卡
     *
     * @param cardC 卡_课程卡
     * @return 结果
     */
     int updateCardC(CardC cardC);

    /**
     * 查询卡_私教,小班，团课，私教列表
     * @param cardc
     * @return
     */
    List<CardC> selectCardList(CardC cardc);

    /**
     * 查询会员拥有的课程卡（包括共享）
     * @param cardC
     * @return
     */
    CardC selectMemberCardC(CardC cardC);

    /**
     * 查询卡_会籍列表
     *
     * @param cardC 卡_会籍
     * @return 卡_会籍集合
     */
    List<CardC> selectMemberDetailsCardCList(CardC cardC);

    /**
     * 查询卡_会籍（共享人可用)
     *
     * @param cardC 卡_会籍主键
     * @return 卡_会籍
     */
    CardC selectMemberDetailsCardC(CardC cardC);

    /**
     * 根据订单id查询课程卡
     * @param orderId
     * @return
     */
    CardC selectCardCByOrderId(@Param("orderId") Long orderId);

    /**
     * 扣卡
     * @param cardC
     * @return
     */
    int deductCard(CardC cardC);

    /**
     * 扣卡 将锁定次数转为消耗次数
     * @param cardC
     * @return
     */
    int deductCardLockToConsume(CardC cardC);

    /**
     * 返还卡内数据
     * @param cardC
     * @return
     */
    int returnCard(CardC cardC);

    /**
     * 获取可用卡
     * @param cardC
     * @return
     */
    List<CardC> listAvailableCard(CardC cardC);
}
