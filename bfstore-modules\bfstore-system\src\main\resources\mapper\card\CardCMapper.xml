<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bf.card.mapper.CardCMapper">

    <resultMap type="CardC" id="CardCResult">
        <result property="id"    column="id"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="storeId"    column="store_id"    />
        <result property="isValid"    column="is_valid"    />
        <result property="cardTemplateCId"    column="card_template_c_id"    />
        <result property="name"    column="name"    />
        <result property="cardNumber"    column="card_number"    />
        <result property="category"    column="category"    />
        <result property="isExperience"    column="is_experience"    />
        <result property="tcType"    column="tc_type"    />
        <result property="status"    column="status"    />
        <result property="courseType"    column="course_type"    />
        <result property="baseSportId"    column="base_sport_id"    />
        <result property="usageStoreType"    column="usage_store_type"    />
        <result property="shareUpper"    column="share_upper"    />
        <result property="leavePeriodUpper" column="leave_period_upper" />
        <result property="leaveCountUpper" column="leave_count_upper" />
        <result property="leaveCountConsume" column="leave_count_consume" />
        <result property="breakAppointmentCountUpper"    column="break_appointment_count_upper"    />
        <result property="breakAppointmentCountConsume"    column="break_appointment_count_consume"    />
        <result property="periodInit"    column="period_init"    />
        <result property="periodActual"    column="period_actual"    />
        <result property="periodDeduct"    column="period_deduct"    />
        <result property="periodReturn"    column="period_return"    />
        <result property="periodTransfer"    column="period_transfer"    />
        <result property="periodGiftInit"    column="period_gift_init"    />
        <result property="periodGiftActual"    column="period_gift_actual"    />
        <result property="periodLeave" column="period_leave" />
        <result property="periodSuspend" column="period_suspend" />
        <result property="timesInit"    column="times_init"    />
        <result property="timesActual"    column="times_actual"    />
        <result property="timesDeduct"    column="times_deduct"    />
        <result property="timesLock"    column="times_lock"    />
        <result property="timesConsume"    column="times_consume"    />
        <result property="timesReturn"    column="times_return"    />
        <result property="timesTransfer"    column="times_transfer"    />
        <result property="timesGiftInit"    column="times_gift_init"    />
        <result property="timesGiftActual"    column="times_gift_actual"    />
        <result property="timesGiftLock"    column="times_gift_lock"    />
        <result property="timesGiftConsume"    column="times_gift_consume"    />
        <result property="priceInit"    column="price_init"    />
        <result property="priceActual"    column="price_actual"    />
        <result property="salesStore"    column="sales_store"    />
        <result property="cardFrom"    column="card_from"    />
        <result property="sourceOrderId"    column="source_order_id"    />
        <result property="applyType"    column="apply_type"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="effectType"    column="effect_type"    />
        <result property="specifyDay"    column="specify_day"    />
        <result property="effectTimeStart"    column="effect_time_start"    />
        <result property="salesChannel"    column="sales_channel"    />
        <result property="orderNum"    column="order_num"    />
        <result property="frontCover"    column="front_cover"    />
        <result property="introduce"    column="introduce"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="signNumber"  column="sign_number" />
        <result property="baseSportName"  column="base_sport_name" />
        <result property="salesUserName"    column="sales_user_name"    />
        <result property="salesUserPhone"    column="sales_user_phone"    />
        <result property="memberId"    column="member_id"    />
        <result property="memberName"    column="member_name"    />
        <result property="memberPhone"    column="member_phone"    />
        <result property="memberSex"    column="member_sex"    />
        <result property="residualValue"    column="residual_value"    />
        <result property="residualAmount"    column="residual_amount"    />
        <result property="residualPeriod"    column="residual_period"    />
        <result property="residualTimes"    column="residual_times"    />
        <result property="effectTimeEnd"    column="effect_time_end"    />
        <result property="lastSignTime"    column="last_sign_time"    />
        <result property="orderFrom"    column="order_from"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="cardTimesDeduct"    column="card_times_deduct"    />
        <result property="storeName" column="store_name" />
        <result property="isBind" column="is_bind" />
        <result property="subStatus" column="sub_status"/>
        <result property="trainingClassName" column="training_class_name" />
        <result property="storeUsable" column="store_usable"/>
        <result property="nameSuffix" column="name_suffix"/>
        <result property="bookingUpper" column="booking_upper"/>
        <result property="crossStoreMode" column="cross_store_mode"/>
        <result property="shareCardMode" column="share_card_mode"/>
        <association property="course">
            <result property="timesDeduct"    column="times_deduct"    />
        </association>
    </resultMap>

    <sql id="selectCardCVo">
        SELECT
            id,tenant_id,store_id,is_valid,card_template_c_id,name,sales_user_id,card_number,category,is_experience,status,
            course_type,base_sport_id,usage_store_type,share_upper,
            period_init,period_actual,times_init,times_actual,times_consume,price_init,price_actual,sales_store,card_from,source_order_id,
            apply_type,apply_time,effect_type,specify_day,effect_time_start,sales_channel,order_num,front_cover,introduce,last_sign_time,
            sign_number,remark,create_time,create_by,
            update_time,update_by
        FROM
            card_c
    </sql>

    <sql id="BaseColumn">
        cc.id,cc.tenant_id,cc.store_id,cc.is_valid,cc.card_template_c_id,cc.name,cc.suffix,
        cc.sales_user_id,cc.card_number,cc.category,cc.is_experience,cc.tc_type,cc.status,cc.course_type,cc.base_sport_id,
        cc.usage_store_type,cc.share_upper, cc.leave_period_upper, cc.leave_count_upper, cc.leave_count_consume, cc.period_init,
        cc.period_actual,cc.period_deduct,cc.period_return,cc.period_transfer,cc.period_gift_init,cc.period_gift_actual,cc.period_leave,
        cc.period_suspend,cc.times_init,cc.times_actual,cc.times_deduct,cc.times_lock,cc.times_consume,cc.times_return,cc.times_transfer,
        cc.times_gift_init,cc.times_gift_actual,cc.times_gift_lock,cc.times_gift_consume,cc.price_init,
        cc.price_actual,cc.sales_store,cc.card_from,cc.source_order_id,cc.apply_type,cc.apply_time,
        cc.effect_type,cc.specify_day,cc.effect_time_start,cc.sales_channel,cc.order_num,cc.front_cover,cc.introduce,
        cc.last_sign_time,cc.sign_number,cc.remark,cc.create_time,cc.create_by,cc.update_time,cc.update_by,
        cc.period_leave,
        <!--剩余价值计算-->
        CASE
        WHEN cc.category = 0 THEN IFNULL(((cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start), 0) - cc.period_deduct - cc.period_return - cc.period_transfer)/cc.period_actual),0) * cc.price_actual
        WHEN cc.category = 1 THEN IFNULL(((cc.times_actual - IFNULL(cc.times_consume, 0) - cc.times_deduct - cc.times_return - cc.times_transfer)/cc.times_actual),0) * cc.price_actual
        ELSE 0
        END AS residual_value,
        <!--剩余数量计算-->
        CASE
        WHEN cc.category = 0 THEN (cc.period_actual + IFNULL(cc.period_gift_actual,0) + cc.period_leave + cc.period_suspend - cc.period_deduct - cc.period_return - cc.period_transfer - IFNULL(DATEDIFF(CURDATE(), effect_time_start),0))
        WHEN cc.category = 1 THEN (cc.times_actual + IFNULL(cc.times_gift_actual,0) - cc.times_deduct - cc.times_return - cc.times_transfer - IFNULL(cc.times_consume,0) - IFNULL(cc.times_gift_consume,0))
        ELSE 0
        END AS residual_amount,
        <!--有效期至-->
        DATE_ADD(cc.effect_time_start, INTERVAL (cc.period_actual + IFNULL(cc.period_gift_actual,0) + cc.period_leave + cc.period_suspend - cc.period_deduct - cc.period_return - cc.period_transfer) DAY) as effect_time_end,
        <!--剩余有效期-->
        cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0) as residual_period,
        <!--剩余次数-->
        cc.times_actual - IFNULL(times_consume,0) as residual_times,
        <!-- 卡状态 -->
        CASE
        WHEN cc.status != 0 then cc.status
        WHEN ((cc.effect_time_start is null and cc.effect_type = 3) or CURRENT_DATE &lt; cc.effect_time_start) THEN 21
        WHEN (cc.effect_time_start is not null and CURRENT_DATE &gt; DATE_ADD(cc.effect_time_start, INTERVAL(cc.period_actual + IFNULL(cc.period_gift_actual, 0) - cc.period_deduct - cc.period_return - cc.period_transfer + cc.period_leave + cc.period_suspend)DAY)) THEN 22
        WHEN (EXISTS (SELECT 1 FROM card_c_leave_records l WHERE l.card_c_id = cc.id AND CURRENT_DATE BETWEEN l.start_date AND DATE_ADD(l.start_date, INTERVAL IFNULL(l.period_actual,l.period) - 1 DAY))) THEN 23
        WHEN (EXISTS (SELECT 1 FROM card_c_suspend_records s WHERE s.card_c_id = cc.id AND ((CURRENT_DATE BETWEEN s.start_date AND DATE_ADD(s.start_date, INTERVAL IFNULL(s.period_actual,s.period) - 1 DAY)) or (s.period is null and s.period_actual is null )))) THEN 24
        ELSE 20
        end as sub_status,
        IF(cc.suffix IS NOT NULL AND cc.suffix != '', CONCAT(cc.name, '(', cc.suffix, ')'), cc.name) AS name_suffix,
    </sql>

    <select id="selectCardList" resultMap="CardCResult">
        select <include refid="BaseColumn"></include>
        s.name as store_name,
        bs.name as base_sport_name,
        m.id AS member_id,m.name AS member_name,m.phonenumber AS member_phone, m.sex as member_sex,
        su.phonenumber AS sales_user_phone,su.name AS sales_user_name,
        ru.store_id AS rel_store_id,
        f.url as avatar_url,
        tc.name as training_class_name
        FROM
        card_c cc
        LEFT JOIN card_c_rel_store_usage ru ON cc.id = ru.card_c_id
        left join tenant_store s on cc.store_id = s.id
        LEFT JOIN card_rel_member crm ON cc.id = crm.card_id
        LEFT JOIN member m ON crm.member_id = m.id
        left join sys_file f ON m.avatar = f.id
        LEFT JOIN sys_user su ON su.user_id = cc.sales_user_id
        left join base_sport bs on cc.base_sport_id = bs.id
        left join training_class_student tcs on tcs.card_id = cc.id
        left join training_class tc on tc.id = tcs.training_class_id
        where crm.is_owner = 1
        <if test="searchValue != null and searchValue != ''">
            and
            (
            m.name like concat('%', #{searchValue}, '%')
            or m.phonenumber like concat('%', #{searchValue}, '%')
            or cc.card_number like concat('%', #{searchValue}, '%')
            or cc.name like concat('%', #{searchValue}, '%')
            )
        </if>
        <if test="category != null ">and cc.category = #{category}</if><!--卡种-->
        <if test="isExperience != null ">and cc.is_experience = #{isExperience}</if><!--是否体验-->
        <if test="status != null">
            <choose>
                <when test="status == 20">
                    <!-- 已开卡 -->
                    and cc.status = 0
                    and cc.effect_time_start is not null
                    and CURDATE() BETWEEN cc.effect_time_start AND DATE_ADD(cc.effect_time_start, INTERVAL (cc.period_actual + IFNULL(cc.period_gift_actual, 0) - cc.period_deduct - cc.period_return - cc.period_transfer + cc.period_leave + cc.period_suspend) DAY)
                    and not exists (
                    select 1 from card_c_leave_records l
                    where l.card_c_id = cc.id
                    and CURDATE() BETWEEN l.start_date and DATE_ADD(l.start_date, INTERVAL IFNULL(l.period_actual, l.period) -1 DAY))
                    and not exists (
                    select 1 from card_c_suspend_records s
                    where s.card_c_id = cc.id
                    and ((CURDATE() BETWEEN s.start_date AND DATE_ADD(s.start_date, INTERVAL IFNULL(s.period_actual,s.period) - 1 DAY)) or (s.period is null and s.period_actual is null )))
                </when>
                <when test="status == 21">
                    <!-- 未开卡 -->
                    and cc.status = 0
                    and ((cc.effect_time_start is null and cc.effect_type = 3) OR CURDATE() &lt; cc.effect_time_start)
                </when>
                <when test="status == 22">
                    <!-- 已过期 -->
                    and cc.status = 0
                    and cc.effect_time_start is not null
                    and CURDATE() &gt; DATE_ADD(cc.effect_time_start, INTERVAL (cc.period_actual + IFNULL(cc.period_gift_actual, 0) - cc.period_deduct - cc.period_return - cc.period_transfer + cc.period_leave + cc.period_suspend) DAY)
                </when>
                <when test="status == 23">
                    <!-- 请假中 -->
                    and cc.status = 0
                    and cc.effect_time_start is not null
                    and exists (
                    select 1 from card_c_leave_records l
                    where l.card_c_id = cc.id
                    and CURDATE() BETWEEN l.start_date and DATE_ADD(l.start_date, INTERVAL IFNULL(l.period_actual, l.period) -1 DAY))
                </when>
                <when test="status == 24">
                    <!-- 停用中 -->
                    and cc.status = 0
                    and cc.effect_time_start is not null
                    and exists (
                    select 1 from card_c_suspend_records s
                    where s.card_c_id = cc.id
                    and ((CURDATE() BETWEEN s.start_date AND DATE_ADD(s.start_date, INTERVAL IFNULL(s.period_actual,s.period) - 1 DAY)) or (s.period is null and s.period_actual is null )))
                </when>
                <when test="status == 30">
                    <!-- 生效卡 包括已开卡，未开卡，请假中，停卡中，已禁用，已锁定 -->
                    and(cc.status = 1 or cc.status = 2 or
                    (cc.status = 0
                    and !(cc.effect_time_start is not null
                    and CURDATE() &gt; DATE_ADD(cc.effect_time_start, INTERVAL (cc.period_actual + IFNULL(cc.period_gift_actual, 0) - cc.period_deduct - cc.period_return - cc.period_transfer + cc.period_leave + cc.period_suspend) DAY))))
                </when>
                <otherwise>
                    <!-- 卡状态 -->
                    and cc.status = #{status}
                </otherwise>
            </choose>
        </if>

        <if test="applyType != null ">and cc.apply_type = #{applyType}</if><!--办卡类型-->
        <if test="params.applyEndTime != null and params.applyEndTime != ''"><!--办卡结束时间-->
            AND date_format(cc.apply_time,'%y%m%d') &lt;= date_format(#{params.applyEndTime},'%y%m%d')
        </if>
        <if test="params.effectStartBeginTime != null and params.effectStartBeginTime != ''"><!-- 开卡开始时间检索 -->
            AND date_format(cc.effect_time_start,'%y%m%d') &gt;= date_format(#{params.effectStartBeginTime},'%y%m%d')
        </if>
        <if test="params.effectStartEndTime != null and params.effectStartEndTime != ''"><!-- 开卡结束时间检索 -->
            AND date_format(cc.effect_time_start,'%y%m%d') &lt;= date_format(#{params.effectStartEndTime},'%y%m%d')
        </if>
        <if test="params.effectBeginTime != null and params.effectBeginTime != ''"><!--有效期至开始时间-->
            AND date_format(DATE_ADD(cc.effect_time_start, INTERVAL (cc.period_actual) DAY),'%y%m%d')
            &gt;= date_format(#{params.effectBeginTime},'%y%m%d')
        </if>
        <if test="params.effectEndTime != null and params.effectEndTime != ''"><!--有效至结束时间-->
            AND date_format(DATE_ADD(cc.effect_time_start, INTERVAL (cc.period_actual) DAY),'%y%m%d')
            &lt;= date_format(#{params.effectEndTime},'%y%m%d')
        </if>
        <if test="params.lastSignBeginTime != null and params.lastSignBeginTime != ''"><!--最后签课开始时间-->
            AND date_format(cc.last_sign_time,'%y%m%d')
            &gt;= date_format(#{params.lastSignBeginTime},'%y%m%d')
        </if>
        <if test="params.lastSignEndTime != null and params.lastSignEndTime != ''"><!--最后签课结束时间-->
            AND date_format(cc.last_sign_time,'%y%m%d')
            &lt;= date_format(#{params.lastSignEndTime},'%y%m%d')
        </if>
        <if test="params.applyBeginTime != null and params.applyBeginTime != ''"><!--办卡时间-->
            AND date_format(cc.apply_time,'%y%m%d')
            &gt;= date_format(#{params.applyBeginTime},'%y%m%d')
        </if>
        <if test="params.applyEndTime != null and params.applyEndTime != ''"><!--办卡时间-->
            AND date_format(cc.apply_time,'%y%m%d')
            &lt;= date_format(#{params.applyEndTime},'%y%m%d')
        </if>
        <if test="courseType != null" >and cc.course_type = #{courseType}</if><!--课程类型-->
        <if test="memberId != null "> and m.id = #{memberId}</if><!--会id-->
        <if test="isValid != null "> and cc.is_valid = #{isValid}</if><!--是否有效-->
        <if test="name != null ">and cc.name = #{name}</if><!--卡课名字-->
        <if test="id != null "> and cc.id = #{id}</if>
        <if test="salesUserId != null ">and cc.sales_user_id = #{salesUserId}</if>
        <if test="storeId != null and storeId !=''">and s.id = #{storeId} </if>
        <if test="remainingFilterType != null and remainingFilterType != ''">
            AND
            <choose>
                <when test="remainingFilterType == 'REMAINING_ZERO'">
                    (CASE
                    WHEN cc.category = 0 THEN (cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
                    WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
                    ELSE 0
                    END) = 0
                </when>
                <when test="remainingFilterType == 'REMAINING_LESS_THAN_10'">
                    (CASE
                    WHEN cc.category = 0 THEN (cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
                    WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
                    ELSE 0
                    END) &gt; 0 AND
                    (CASE
                    WHEN cc.category = 0 THEN (cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
                    WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
                    ELSE 0
                    END) &lt; 10
                </when>
                <when test="remainingFilterType == 'REMAINING_LESS_THAN_20'">
                    (CASE
                    WHEN cc.category = 0 THEN (cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
                    WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
                    ELSE 0
                    END) &gt; 0 AND
                    (CASE
                    WHEN cc.category = 0 THEN (cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
                    WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
                    ELSE 0
                    END) &lt;= 20
                </when>
                <when test="remainingFilterType == 'REMAINING_BETWEEN_10_AND_20'">
                    (CASE
                    WHEN cc.category = 0 THEN (cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
                    WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
                    ELSE 0
                    END) &gt;= 10 AND
                    (CASE
                    WHEN cc.category = 0 THEN (cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
                    WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
                    ELSE 0
                    END) &lt;= 20
                </when>
                <when test="remainingFilterType == 'REMAINING_CUSTOM' and customMinRemaining != null and customMaxRemaining != null">
                    (CASE
                    WHEN cc.category = 0 THEN (cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
                    WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
                    ELSE 0
                    END) &gt;= #{customMinRemaining} AND
                    (CASE
                    WHEN cc.category = 0 THEN (cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
                    WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
                    ELSE 0
                    END) &lt;= #{customMaxRemaining}
                </when>
                <when test="remainingFilterType == 'REMAINING_CUSTOM' and customMinRemaining != null and customMaxRemaining == null">
                    (CASE
                    WHEN cc.category = 0 THEN (cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
                    WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
                    ELSE 0
                    END) &gt;= #{customMinRemaining}
                </when>
                <when test="remainingFilterType == 'REMAINING_CUSTOM' and customMinRemaining == null and customMaxRemaining != null">
                    (CASE
                    WHEN cc.category = 0 THEN (cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
                    WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
                    ELSE 0
                    END) &lt;= #{customMaxRemaining}
                </when>
            </choose>
        </if>
        <if test="isAssigned != null">
            <choose>
                <when test="isAssigned == 1">
                    and exists (
                    select 1 from training_class_student tcs
                    where tcs.card_id = cc.id
                    )
                </when>
                <when test="isAssigned == 0">
                    and not exists (
                    select 1 from training_class_student tcs
                    where tcs.card_id = cc.id
                    )
                </when>
            </choose>
        </if>
        <if test="statusRange != null">
            and (
            <foreach collection="statusRange" item="status" separator=" or ">
                <choose>
                    <when test="status == 20">
                        (cc.status = 0
                        and cc.effect_time_start is not null
                        and CURDATE() BETWEEN cc.effect_time_start AND DATE_ADD(cc.effect_time_start, INTERVAL (cc.period_actual + IFNULL(cc.period_gift_actual, 0) - cc.period_deduct - cc.period_return - cc.period_transfer + cc.period_leave + cc.period_suspend) DAY)
                        and not exists (
                        select 1 from card_c_leave_records l
                        where l.card_c_id = cc.id
                        and CURDATE() BETWEEN l.start_date and DATE_ADD(l.start_date, INTERVAL IFNULL(l.period_actual, l.period) -1 DAY))
                        and not exists (
                        select 1 from card_c_suspend_records s
                        where s.card_c_id = cc.id
                        and ((CURDATE() BETWEEN s.start_date AND DATE_ADD(s.start_date, INTERVAL IFNULL(s.period_actual,s.period) - 1 DAY)) or (s.period is null and s.period_actual is null ))))
                    </when>
                    <when test="status == 21">
                        (cc.status = 0
                        and ((cc.effect_time_start is null and cc.effect_type = 3) OR CURDATE() &lt; cc.effect_time_start))
                    </when>
                    <when test="status == 22">
                        (cc.status = 0
                        and cc.effect_time_start is not null
                        and CURDATE() &gt; DATE_ADD(cc.effect_time_start, INTERVAL (cc.period_actual + IFNULL(cc.period_gift_actual, 0) - cc.period_deduct - cc.period_return - cc.period_transfer + cc.period_leave + cc.period_suspend) DAY))
                    </when>
                    <when test="status == 23">
                        (cc.status = 0
                        and cc.effect_time_start is not null
                        and exists (
                        select 1 from card_c_leave_records l
                        where l.card_c_id = cc.id
                        and CURDATE() BETWEEN l.start_date and DATE_ADD(l.start_date, INTERVAL IFNULL(l.period_actual, l.period) -1 DAY)))
                    </when>
                    <when test="status == 24">
                        (cc.status = 0
                        and cc.effect_time_start is not null
                        and exists (
                        select 1 from card_c_suspend_records s
                        where s.card_c_id = cc.id
                        and ((CURDATE() BETWEEN s.start_date AND DATE_ADD(s.start_date, INTERVAL IFNULL(s.period_actual,s.period) - 1 DAY)) or (s.period is null and s.period_actual is null ))))
                    </when>
                </choose>
            </foreach>
            )
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by cc.update_time desc
    </select>

    <select id="selectCardCById"  resultMap="CardCResult">
        select <include refid="BaseColumn"></include>
        s.name as store_name,
        bs.name as base_sport_name,
        m.id as member_id,m.name as member_name,m.phonenumber as member_phone, m.sex as member_sex,
        su.phonenumber as sales_user_phone,su.nick_name as sales_user_name,
        ru.store_id as rel_store_id,
        f.url as avatar_url,
        ctc.cross_store_mode,
        ctc.share_card_mode
        from card_c cc
        left join card_c_rel_store_usage ru on cc.id = ru.card_c_id
        left join tenant_store s on cc.store_id = s.id
        left join card_rel_member crm on cc.id = crm.card_id and crm.is_owner = 1
        left join member m on crm.member_id = m.id
        left join sys_user su on su.user_id = cc.sales_user_id
        left join sys_file f on m.avatar = f.id
        left join base_sport bs on cc.base_sport_id = bs.id
        left join card_template_c ctc on ctc.id = cc.card_template_c_id
        where cc.id = #{id} and crm.is_owner = 1
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectMemberCardC" resultMap="CardCResult">
        select <include refid="BaseColumn"></include>
        s.name as store_name,
        bs.name as base_sport_name,
        m.id as member_id,m.name as member_name,m.phonenumber as member_phone, m.sex as member_sex,
        su.phonenumber as sales_user_phone,su.name as sales_user_name,
        ru.store_id as rel_store_id
        from card_c cc
        left join card_c_rel_store_usage ru on cc.id = ru.card_c_id
        left join tenant_store s on cc.store_id = s.id
        left join card_rel_member crm on cc.id = crm.card_id and crm.is_owner = 1
        left join member m on crm.member_id = m.id
        left join sys_user su on su.user_id = cc.sales_user_id
        left join base_sport bs on cc.base_sport_id = bs.id
        where 1=1
        <if test="id != null "> and cc.id = #{id}</if>
        <if test="memberId != null"> and crm.member_id = #{memberId}</if>
        <if test="storeId != null and storeId !=''">and s.id = #{storeId} </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectMemberDetailsCardCList" resultMap="CardCResult">
        select <include refid="BaseColumn"></include>
        case when
            (select count(*) from card_c_rel_store_usage ru
            where card_c_id = cc.id and ru.store_id = #{storeId})
        = 0 THEN 0 else 1
        end as store_usable,
        s.name as store_name,
        bs.name as base_sport_name,
        m.id AS member_id,m.name AS member_name,m.phonenumber AS member_phone, m.sex as member_sex,
        su.phonenumber AS sales_user_phone,su.name AS sales_user_name,
        #{storeId} as rel_store_id
        FROM
        card_c cc
        left join tenant_store s on cc.store_id = s.id
        LEFT JOIN card_rel_member crm ON cc.id = crm.card_id
        LEFT JOIN member m ON crm.member_id = m.id
        left join sys_file f ON m.avatar = f.id
        LEFT JOIN sys_user su ON su.user_id = cc.sales_user_id
        left join base_sport bs on cc.base_sport_id = bs.id
        where crm.is_owner = 1 and crm.member_id = #{memberId}
        <if test="courseType != null">and cc.course_type = #{courseType}</if>
        order by store_usable desc, cc.update_time desc
    </select>

    <select id="selectMemberDetailsCardC"  resultMap="CardCResult">
        select <include refid="BaseColumn"></include>
        case when
            (select count(*) from card_c_rel_store_usage ru
            where card_c_id = cc.id and ru.store_id = #{storeId})
        = 0 THEN 0 else 1
        end as store_usable,
        s.name as store_name,
        bs.name as base_sport_name,
        m.id as member_id,m.name as member_name,m.phonenumber as member_phone, m.sex as member_sex,
        su.phonenumber as sales_user_phone,su.nick_name as sales_user_name,
        #{storeId} as rel_store_id,
        f.url as avatar_url
        from card_c cc
        left join tenant_store s on cc.store_id = s.id
        left join card_rel_member crm on cc.id = crm.card_id
        left join member m on crm.member_id = m.id
        left join sys_user su on su.user_id = cc.sales_user_id
        left join sys_file f on m.avatar = f.id
        left join base_sport bs on cc.base_sport_id = bs.id
        left join card_template_c ctc on ctc.id = cc.card_template_c_id
        where cc.id = #{id} and crm.is_owner = 1
    </select>


    <select id="selectCardCByOrderId" resultMap="CardCResult">
        select <include refid="BaseColumn"></include>
        s.name as store_name
        from card_c cc
        left join tenant_store s on cc.store_id = s.id
        where cc.source_order_id = #{orderId} and cc.card_from = 0
    </select>

    <update id="updateCardC" parameterType="CardC">
        update card_c
        <set>
            <if test="periodLeave != null">
                period_leave = period_leave + #{periodLeave},
            </if>
            <if test="cardNumber != null">
                card_number = #{cardNumber},
            </if>
            <if test="periodActual != null">
                period_actual = period_actual + #{periodActual},
            </if>
            <if test="periodDeduct != null">
                period_deduct = period_deduct + #{periodDeduct},
            </if>
            <if test="periodGiftActual != null">
                period_gift_actual = period_gift_actual + #{periodGiftActual},
            </if>
            <if test="periodSuspend != null">
                period_suspend = period_suspend + #{periodSuspend},
            </if>
            <if test="timesDeduct != null">
                times_deduct = times_deduct + #{timesDeduct},
            </if>
            <if test="timesActual != null">
                times_deduct = times_deduct + #{timesDeduct},
            </if>
            <if test="timesGiftActual != null">
                times_gift_actual = times_gift_actual + #{timesGiftActual},
            </if>
            <if test="periodReturn != null">
                period_return = period_return + #{periodReturn},
            </if>
            <if test="periodTransfer != null">
                period_transfer = period_transfer + #{periodTransfer},
            </if>
            <if test="timesReturn != null">
                times_return = times_return + #{timesReturn},
            </if>
            <if test="timesTransfer != null">
                times_transfer = times_transfer + #{timesTransfer},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <!--开卡操作 -->
            <if test="effectTimeStart != null">
                effect_time_start = #{effectTimeStart},
            </if>
        </set>
        where id = #{id}
        <if test="periodActual != null">
            and period_actual + #{periodActual} &gt;= 0
        </if>
        <if test="periodDeduct != null">
            and (period_deduct + #{periodDeduct} &gt;= 0 and (period_deduct + #{periodDeduct} + period_return + period_transfer) &lt;= period_actual)
        </if>
        <if test="periodTransfer != null">
            and (period_transfer + #{periodTransfer} + period_return + period_deduct) &lt;= period_actual
        </if>
        <if test="periodGiftActual != null">
            and period_gift_actual + #{periodGiftActual} &gt;= 0
        </if>
        <if test="timesDeduct != null">
            and (times_deduct + #{timesDeduct} &gt;= 0 and (times_deduct + #{timesDeduct} + times_return + times_transfer + times_lock + times_consume) &lt;= times_actual)
        </if>
        <if test="timesTransfer != null">
            and (times_transfer + #{timesTransfer} + times_return + times_deduct + times_lock + times_consume) &lt;= times_actual
        </if>
        <if test="timesGiftActual != null">
            and times_gift_actual + #{timesGiftActual} - times_gift_lock - times_gift_consume &gt;= 0
        </if>
        <if test="periodReturn != null">
            and (period_deduct + #{periodReturn} + period_return + period_transfer) &lt;= period_actual
        </if>
        <if test="timesReturn != null">
            and (times_deduct + #{timesReturn} + times_return + times_transfer + times_lock + times_consume) &lt;= times_actual
        </if>
    </update>

    <update id="deductCard"  parameterType="CardMs">
        update card_c
        <set>
            <if test="timesConsume != null">
                times_consume = times_consume + #{timesConsume},
            </if>
            <if test="timesGiftConsume != null">
                times_gift_consume = times_gift_consume + #{timesGiftConsume},
            </if>
            <if test="timesLock != null">
                times_lock = times_lock + #{timesLock},
            </if>
            <if test="timesGiftLock != null">
                times_gift_lock = times_gift_lock + #{timesGiftLock},
            </if>
        </set>
        where id = #{id}
        <if test="timesConsume != null">
            and times_actual - times_consume - #{timesConsume} - times_deduct - times_lock - times_return - times_transfer &gt;= 0
        </if>
        <if test="timesGiftConsume != null">
            and times_gift_actual - times_gift_consume - #{timesGiftConsume} - times_gift_lock &gt;= 0
        </if>
        <if test="timesLock != null">
            and times_actual - times_consume - times_deduct - times_lock - #{timesLock} - times_return - times_transfer &gt;= 0
        </if>
        <if test="timesGiftLock != null">
            and times_gift_actual - times_gift_consume - times_gift_lock - #{timesGiftLock} &gt;= 0
        </if>
    </update>

    <update id="deductCardLockToConsume"  parameterType="CardMs">
        update card_c
        <set>
            <if test="timesLock != null">
                times_lock = times_lock - #{timesLock},
                times_consume = times_consume + #{timesLock},
            </if>
            <if test="timesGiftLock != null">
                times_gift_lock = times_gift_lock - #{timesGiftLock},
                times_gift_consume = times_gift_consume + #{timesGiftLock},
            </if>
        </set>
        where id = #{id}
        <if test="timesLock != null">
            and (times_actual - times_consume - times_deduct - times_lock + #{timesLock} - times_return - times_transfer &gt;= 0
            and times_lock - #{timesLock} &gt;= 0)
        </if>
        <if test="timesGiftLock != null">
            and (times_gift_actual - times_gift_consume - times_gift_lock + #{timesGiftLock} &gt;= 0 and
            times_gift_lock - #{timesGiftLock} &gt;= 0)
        </if>
    </update>

    <update id="returnCard">
        update card_c
        <set>
            <if test="timesConsume != null">times_consume = times_consume - #{timesConsume},</if>
            <if test="timesGiftConsume != null">times_gift_consume = times_gift_consume - #{timesGiftConsume},</if>
            <if test="timesLock != null">times_lock = times_lock - #{timeslock},</if>
            <if test="timesGiftLock != null">times_gift_lock = times_gift_lock - #{timesGiftlock},</if>
        </set>
        where id = #{id}
        <if test="timesConsume != null">
            and times_consume &gt;= #{timesConsume}
        </if>
        <if test="timesGiftConsume != null">
            and times_gift_consume &gt;= #{timesGiftConsume}
        </if>
        <if test="timesLock != null">
            and times_lock &gt;= #{timeslock}
        </if>
        <if test="timesGiftLock != null">
            and times_gift_lock &gt;= #{timesGiftlock}
        </if>
    </update>

    <select id="listAvailableCard" parameterType="CardC"  resultMap="CardCResult">
        select <include refid="BaseColumn"></include>
        s.name as store_name,
        m.id as member_id,m.name as member_name,m.phonenumber as member_phone, m.sex as member_sex,
        su.phonenumber as sales_user_phone,su.nick_name as sales_user_name,
        ru.store_id as rel_store_id,
        f.url as avatar_url,
        bs.name as base_sport_name,
        cl.booking_upper,
        c.times_deduct as card_times_deduct
        <if test="trainingClassFlag != null and trainingClassFlag == 1">
            ,case when cs.card_id is null then 0
            else 1
            end as is_bind,
            tc.name as training_class_name
        </if>
        from card_c cc
        left join card_c_rel_store_usage ru on cc.id = ru.card_c_id
        left join tenant_store s on ru.store_id = s.id
        left join card_rel_member crm1 on cc.id = crm1.card_id
        left join card_rel_member crm2 on cc.id = crm2.card_id
        left join member m on crm2.member_id = m.id
        left join sys_user su on su.user_id = cc.sales_user_id
        left join sys_file f on m.avatar = f.id
        left join card_template_c_course c on c.card_template_c_id = cc.card_template_c_id
        left join card_c_coach co on co.card_c_id = cc.id
        left join base_sport bs on bs.id = cc.base_sport_id
        left join card_template_c_limit cl on cl.card_template_c_id = cc.card_template_c_id
        <if test="trainingClassFlag != null and trainingClassFlag == 1">
        left join training_class_student cs on cs.card_id = cc.id
        left join training_class tc on tc.id = cs.training_class_id
        </if>
        where cc.is_valid = 1 and
        ((DATE_ADD(cc.effect_time_start, INTERVAL cc.period_actual DAY) > CURDATE())
        or (cc.effect_type = 3 and cc.effect_time_start is null ) or cc.effect_time_start &gt; CURDATE())
        and not exists (
        select 1 from card_c_leave_records l
        where l.card_c_id = cc.id
        and CURDATE() BETWEEN l.start_date and DATE_ADD(l.start_date, INTERVAL IFNULL(l.period_actual, l.period) -1 DAY))
        and not exists (
        select 1 from card_c_suspend_records s
        where s.card_c_id = cc.id
        and ((CURDATE() BETWEEN s.start_date AND DATE_ADD(s.start_date, INTERVAL IFNULL(s.period_actual,s.period) - 1 DAY)) or (s.period is null and s.period_actual is null )))
        <if test="memberId != null">
            and crm1.member_id = #{memberId}
        </if>
        <if test="ignoreMemberIds != null">
            and crm1.member_id not in
            <foreach collection="ignoreMemberIds" item="memberId" open="(" separator="," close=")">
                #{memberId}
            </foreach>
        </if>
        and c.course_id = #{courseId}
        and cc.course_type = #{courseType} and crm2.is_owner = 1 and cc.status = 0
        <if test="coachId != null">
            and co.coach_id = #{coachId}
        </if>
        <if test="coachIds != null">
            and co.coach_id in
            <foreach collection="coachIds" item="coachId" open="(" separator="," close=")">
                #{coachId}
            </foreach>
        </if>
        <if test="searchValue != null and searchValue != ''"><!-- 关键字检索 -->
            and ( cc.name like concat('%', #{searchValue}, '%') )
        </if>
        <if test="id != null">
            and cc.id = #{id}
        </if>
        <if test="trainingClassFlag != null and trainingClassFlag == 1">
            and crm1.is_owner = 1
        </if>
        <if test="cardTemplateCId != null">
            and cc.card_template_c_id = #{cardTemplateCId}
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        <if test="trainingClassFlag != null and trainingClassFlag == 1">
            order by case 
            when cs.card_id is null then 0
            else 1
            end
        </if>
    </select>

</mapper>
