package com.bf.card.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bf.card.domain.*;
import com.bf.card.domain.vo.CardCVo;
import com.bf.card.mapper.CardCMapper;
import com.bf.card.service.ICardCCoachService;
import com.bf.card.service.ICardCRelStoreUsageService;
import com.bf.card.service.ICardCService;
import com.bf.card.service.ICardRelMemberService;
import com.bf.channel.domain.ChannelOrderPay;
import com.bf.channel.mapper.ChannelOrderPayMapper;
import com.bf.common.core.constant.CardConstants;
import com.bf.common.core.constant.CourseConstants;
import com.bf.common.core.constant.OrderConstants;
import com.bf.common.core.constant.UserConstants;
import com.bf.common.core.exception.ServiceException;
import com.bf.common.core.utils.DateUtils;
import com.bf.common.core.utils.SpringUtils;
import com.bf.common.core.utils.StringUtils;
import com.bf.common.datascope.annotation.StoreDataScope;
import com.bf.common.pay.constant.PayConstants;
import com.bf.common.security.utils.SecurityUtils;
import com.bf.member.domain.MemberRelStoreUsage;
import com.bf.member.mapper.MemberMapper;
import com.bf.member.mapper.MemberRelStoreUsageMapper;
import com.bf.order.domain.OrderCardAdditional;
import com.bf.order.domain.OrderCardSales;
import com.bf.order.domain.OrderCardSalesPerformance;
import com.bf.order.domain.vo.OrderCardAdditionalVo;
import com.bf.order.mapper.OrderCardSalesMapper;
import com.bf.order.mapper.OrderCardSalesPerformanceMapper;
import com.bf.order.service.IOrderCardAdditionalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 卡_课程卡Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class CardCServiceImpl extends ServiceImpl<CardCMapper, CardC> implements ICardCService
{

    @Autowired
    private CardCMapper cardCMapper;

    @Autowired
    private ICardRelMemberService cardRelMemberService;

    @Autowired
    private ICardCCoachService cardCCoachService;

    @Autowired
    private ICardCRelStoreUsageService cardCRelStoreUsageService;

    @Autowired
    private MemberMapper memberMapper;
    
    @Autowired
    private MemberRelStoreUsageMapper memberRelStoreUsageMapper;

    @Autowired
    private OrderCardSalesMapper orderCardSalesMapper;

    @Autowired
    private OrderCardSalesPerformanceMapper orderCardSalesPerformanceMapper;

    @Autowired
    private ChannelOrderPayMapper channelOrderPayMapper;

    @Autowired
    private com.bf.card.service.ICardTemplateCService cardTemplateCService;

    /**
     * 查询卡_课程卡
     *
     * @param id 卡_课程卡主键
     * @return 卡_课程卡
     */
    @Override
    public CardC selectCardCById(Long id)
    {
        CardC cardC = new CardC();
        cardC.setId(id);
        return SpringUtils.getAopProxy(this).selectCardC(cardC);
    }

    @Override
    @StoreDataScope(storeAlias = "ru")
    public CardC selectCardC(CardC cardC)
    {
        return this.baseMapper.selectCardCById(cardC);
    }
    
    @Override
    public int insertCardC(CardCVo cardCVO)
    {
        for(CardRelMember member : cardCVO.getMemberList()){
            CardC cardC = cardCVO.getCardC();
            cardC.setId(member.getCardId());
            cardC.setCardNumber(member.getCardNumber());
            // 自动补全tcType字段
            if (cardC.getTcType() == null && cardC.getCardTemplateCId() != null) {
                // 通过模板id查找tcType
                CardTemplateC template = cardTemplateCService.selectCardTemplateCById(cardC.getCardTemplateCId());
                if (template != null) {
                    cardC.setTcType(template.getTcType());
                }
            }
            //课程卡
            cardCMapper.insert(cardC);
            //课程卡会员
            member.setId(null);
            cardRelMemberService.save(member);
            List<CardCRelStoreUsage> storeList = StringUtils.isNull(cardCVO.getStoreList())?new ArrayList<CardCRelStoreUsage>():cardCVO.getStoreList();
            //课程卡可用门店
            if(cardC.getUsageStoreType() == CardConstants.USAGE_STORE_TYPE_SALE){
                CardCRelStoreUsage storeUsage = new CardCRelStoreUsage();
                storeUsage.setCardCId(cardC.getId());
                storeUsage.setStoreId(cardC.getSalesStore());
                cardCRelStoreUsageService.save(storeUsage);
                storeList.add(storeUsage);
            }
            else{
                if(StringUtils.isNotEmpty(cardCVO.getStoreList())){
                    cardCVO.getStoreList().forEach(store -> {store.setCardCId(cardC.getId());store.setId(null);});
                    cardCRelStoreUsageService.saveBatch(cardCVO.getStoreList());
                }
            }
            //课程卡教练
            if(StringUtils.isNotEmpty(cardCVO.getCoachList())){
                cardCVO.getCoachList().forEach(coach -> {coach.setCardCId(cardC.getId());coach.setId(null);});
                cardCCoachService.saveBatch(cardCVO.getCoachList());
            }

            //新增会员可用门店
            if(StringUtils.isNotEmpty(storeList)){
                for (CardCRelStoreUsage cardCRelStoreUsage : storeList) {
                    LambdaQueryWrapper<MemberRelStoreUsage> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(MemberRelStoreUsage::getMemberId,member.getMemberId())
                            .eq(MemberRelStoreUsage::getTenantId,member.getTenantId())
                            .eq(MemberRelStoreUsage::getStoreId,cardCRelStoreUsage.getStoreId());
                    MemberRelStoreUsage info = memberRelStoreUsageMapper.selectOne(queryWrapper);
                    if(StringUtils.isNull(info)){
                        MemberRelStoreUsage memberRelStoreUsage = new MemberRelStoreUsage();
                        memberRelStoreUsage.setTenantId(member.getTenantId());
                        memberRelStoreUsage.setStoreId(cardCRelStoreUsage.getStoreId());
                        memberRelStoreUsage.setMemberId(member.getMemberId());
                        memberRelStoreUsageMapper.insert(memberRelStoreUsage);
                    }
                    //更新会员购卡数量
                    memberMapper.updateCardNumber(CardConstants.CARD_TYPE_C, cardC.getCourseType(), member.getMemberId(), cardCRelStoreUsage.getStoreId());
                }
            }
        }
        return UserConstants.DATABASE_SUCCESS;
    }

    /**
     * 修改卡_课程卡
     *
     * @param cardC 卡_课程卡
     * @return 结果
     */
    @Override
    public int updateCardC(CardC cardC)
    {
        return cardCMapper.updateCardC(cardC);
    }



    /**
     * 查询卡_私教,小班，团课，培训列表
     * @param cardc
     * @return
     */
    @Override
    @StoreDataScope(storeAlias = "ru")
    public List<CardC> selectCardList(CardC cardc)
    {
        return cardCMapper.selectCardList(cardc);
    }


    @Override
    public List<CardC> selectCardListMemberDetails(CardC cardC) {
        cardC.setStoreId(SecurityUtils.getStore());
        return this.baseMapper.selectMemberDetailsCardCList(cardC);
    }

    @Override
    public CardC selectCardCByIdMemberDetails(Long id) {
        CardC cardC = new CardC();
        cardC.setId(id);
        cardC.setStoreId(SecurityUtils.getStore());
        return this.baseMapper.selectMemberDetailsCardC(cardC);
    }

    @Override
    public CardC selectCardCByOrderId(Long orderId) {
        return this.baseMapper.selectCardCByOrderId(orderId);
    }


    /**
     * 构建课程卡vo
     *
     * @param cardCVo
     * @return
     */
    @Override
    public CardCVo buildCardCVo(CardCVo cardCVo)
    {
        CardC cardC = cardCVo.getCardC();

        //课程卡可用门店
        List<CardCRelStoreUsage> cardCRelStoreUsageList = cardCRelStoreUsageService.selectCardCRelStoreUsageList(cardC.getId());
        cardCVo.setStoreList(cardCRelStoreUsageList);
        return cardCVo;
    }

    @Override
    public CardC deductCard(Long memberId, Long deductCardId, Integer deductCardTimesTotal) {
        return this.deductCard(memberId, deductCardId, deductCardTimesTotal, false);
    }

    @Override
    public CardC deductCard(Long memberId, Long deductCardId, Integer deductCardTimesTotal, Boolean needLock)
    {
        CardC query = new CardC();
        query.setId(deductCardId);
        query.setMemberId(memberId);
        CardC cardC = SpringUtils.getAopProxy(this).selectMemberCardC(query);
        if(StringUtils.isNull(cardC) || cardC.getCategory() != CardConstants.CATEGORY_TIMES){
            throw new ServiceException("卡不存在或卡种错误");
        }
        //抵扣次数为0，直接返回
        if(deductCardTimesTotal == 0){
            cardC.setConsumeValue(BigDecimal.ZERO);
            cardC.setTimesConsume(0);
            cardC.setTimesGiftConsume(0);
            return cardC;
        }
        // 计算次数
        Integer timesConsume = handleDeductTimes(cardC.getTimesActual(), cardC.getTimesConsume() + cardC.getTimesDeduct()
                + cardC.getTimesLock() + cardC.getTimesReturn() + cardC.getTimesTransfer(), deductCardTimesTotal);
        Integer timesGiftConsume = 0;
        if(timesConsume < deductCardTimesTotal){
            //剩余次数不足
            timesGiftConsume = handleDeductTimes(cardC.getTimesGiftActual(), cardC.getTimesGiftConsume() + cardC.getTimesGiftLock(),
                    deductCardTimesTotal - timesConsume);
            if(timesGiftConsume.compareTo(deductCardTimesTotal - timesConsume) < 0 ){
                throw new ServiceException("卡内次数不足");
            }
        }
        if (timesConsume == 0 && timesGiftConsume == 0){
            throw new ServiceException("卡内次数不足");
        }
        // 计算消耗价值
        //timesConsume / timesActual * priceActual;
        BigDecimal deductCardConsume = NumberUtil.mul(NumberUtil.div(timesConsume, cardC.getTimesActual()),cardC.getPriceActual());
        cardC.setConsumeValue(deductCardConsume);

        if(needLock){
            //锁定卡内次数
            cardC.setTimesLock(timesConsume);
            cardC.setTimesGiftLock(timesGiftConsume);
            cardC.setTimesConsume(null);
            cardC.setTimesGiftConsume(null);
        }
        int rows = this.baseMapper.deductCard(cardC);
        if(rows == 0){
            throw new ServiceException("卡内次数不足");
        }
        cardC.setTimesConsume(timesConsume);
        cardC.setTimesGiftConsume(timesGiftConsume);

        //未开卡时 开卡
        if(cardC.getSubStatus() == CardConstants.CARD_STATUS_NOTOPENED) {
            OrderCardAdditional orderCardAdditional = new OrderCardAdditional();
            OrderCardAdditionalVo orderCardAdditionalVo = new OrderCardAdditionalVo();
            orderCardAdditionalVo.setOrderCardAdditional(orderCardAdditional);
            orderCardAdditional.setOperateType(OrderConstants.OPERATE_TYPE_OPEN);
            orderCardAdditional.setCardType(CardConstants.CARD_TYPE_C);
            orderCardAdditional.setCardId(cardC.getId());
            orderCardAdditional.setCardC(cardC);
            SpringUtils.getBean(IOrderCardAdditionalService.class).insertOrderCardAdditionalPay(orderCardAdditionalVo);
        }

        return cardC;
    }

    @Override
    public void deductCard(Long deductCardId,Integer timesLock, Integer timesGiftLock) {
        //抵扣次数为0，直接返回
        if((timesLock == null && timesGiftLock == null) || (timesLock == 0 && timesGiftLock == 0)){
            return;
        }
        CardC updateCardC = new CardC();
        updateCardC.setId(deductCardId);
        updateCardC.setTimesLock(timesLock);
        updateCardC.setTimesGiftLock(timesGiftLock);
        int rows = this.baseMapper.deductCardLockToConsume(updateCardC);
        if(rows == 0){
            throw new ServiceException("卡内次数错误");
        }
    }

    /**
     * 计算出对应扣卡次数
     * @param timesActual
     * @param timesConsume
     * @param deductCardTimesTotal
     * @return
     */
    private Integer handleDeductTimes(Integer timesActual, Integer timesConsume, Integer deductCardTimesTotal)
    {
        if(timesActual == timesConsume){
            return 0;
        }
        else if(timesActual > timesConsume){
            if(timesActual - timesConsume < deductCardTimesTotal){
                throw new ServiceException("卡内次数不足");
            }else {
                return deductCardTimesTotal;
            }
        }{
            throw new ServiceException("卡内次数有误");
        }
    }

    @Override
    @StoreDataScope(storeAlias = "ru")
    public CardC selectMemberCardC(CardC cardC)
    {
        return this.baseMapper.selectMemberCardC(cardC);
    }

    @Override
    public CardC returnCard(Long cardId, Integer cardTimes, Integer cardTimesGift) {
        return this.returnCard(cardId, cardTimes, cardTimesGift, false);
    }

    @Override
    public CardC returnCard(Long cardId, Integer cardTimes, Integer cardTimesGift, Boolean returnLock) {
        CardC cardC = this.selectCardCById(cardId);
        if(StringUtils.isNull(cardC) || cardC.getCategory() != CardConstants.CATEGORY_TIMES){
            throw new ServiceException("卡不存在或卡种错误");
        }
        //空值处理
        if(StringUtils.isNull(cardTimes) && StringUtils.isNull(cardTimesGift)){
            cardC.setTimesConsume(Integer.valueOf(0));
            cardC.setTimesGiftConsume(Integer.valueOf(0));
            cardC.setConsumeValue(BigDecimal.ZERO);
            return cardC;
        }
        if(StringUtils.isNull(cardTimes)){
            cardTimes = Integer.valueOf(0);
        }
        if(StringUtils.isNull(cardTimesGift)){
            cardTimesGift = Integer.valueOf(0);
        }
        // 计算消耗价值
        // (cardTimes+ cardTimesGift) / (timesActual + timesGiftActual) * priceActual;
        BigDecimal deductCardConsume = NumberUtil.div(NumberUtil.mul(NumberUtil.add(cardTimes, cardTimesGift),
                cardC.getPriceActual()), NumberUtil.add(cardC.getTimesActual(), cardC.getTimesGiftActual()));
        cardC.setConsumeValue(deductCardConsume);
        int rows = 0;
        //返卡
        if(returnLock){
            cardC.setTimesLock(cardTimes);
            cardC.setTimesGiftLock(cardTimesGift);
            cardC.setTimesConsume(null);
            cardC.setTimesGiftConsume(null);
        }else{
            cardC.setTimesConsume(cardTimes);
            cardC.setTimesGiftConsume(cardTimesGift);
        }
        if (cardTimes >= 0 || cardTimesGift >= 0){
            rows = this.baseMapper.returnCard(cardC);
        }
        if(rows == 0){
            throw new ServiceException("返还卡内次数错误");
        }
        cardC.setTimesConsume(cardTimes);
        cardC.setTimesGiftConsume(cardTimesGift);
        return cardC;
    }

    /**
     * 获取可用卡
     * @param cardC
     * @return
     */
    @Override
    @StoreDataScope(storeAlias = "ru,c",conditionType = 1)
    public List<CardC> listAvailableCard(CardC cardC)
    {
        cardC.setStoreId(SecurityUtils.getStore());
        return this.baseMapper.listAvailableCard(cardC);
    }

    @Override
    public boolean assignCoach(CardCCoach cardCCoach, Integer courseType)
    {
        List<CardCCoach> list = new ArrayList<>();

        // 分配教练时间：直接使用当前时间
        Date allocateTime = DateUtils.getNowDate();

        // 兼容处理：支持单个值和数组两种方式
        Long[] cardCIds = cardCCoach.getCardCIds();
        Long[] coachIds = cardCCoach.getCoachIds();

        // 如果数组为空，但单个值不为空，则使用单个值
        if ((cardCIds == null || cardCIds.length == 0) && cardCCoach.getCardCId() != null) {
            cardCIds = new Long[]{cardCCoach.getCardCId()};
        }
        if ((coachIds == null || coachIds.length == 0) && cardCCoach.getCoachId() != null) {
            coachIds = new Long[]{cardCCoach.getCoachId()};
        }

        // 验证参数
        if (cardCIds == null || cardCIds.length == 0) {
            throw new ServiceException("课程卡ID不能为空");
        }
        if (coachIds == null || coachIds.length == 0) {
            throw new ServiceException("教练ID不能为空");
        }

        for(Long cardCId : cardCIds){
            CardC cardC = SpringUtils.getAopProxy(this).selectCardCById(cardCId);
            if (StringUtils.isNull(cardC) || cardC.getCourseType() != courseType) {
                throw new ServiceException("卡不存在");
            }
            for (Long coachId : coachIds) {
                CardCCoach coach = new CardCCoach();
                coach.setCardCId(cardCId);
                coach.setCoachId(coachId);
                coach.setAllocateTime(allocateTime); // 使用处理后的分配时间
                coach.setMainCoach(0);
                list.add(coach);
            }
        }
        return cardCCoachService.saveBatch(list);
    }

    @Override
    @Transactional
    public int removeCoach(CardCCoach cardCCoach, Integer courseType)
    {
        for(Long cardCId : cardCCoach.getCardCIds()){
            CardC cardC = SpringUtils.getAopProxy(this).selectCardCById(cardCId);
            if (StringUtils.isNull(cardC) || cardC.getCourseType() != courseType) {
                throw new ServiceException("卡不存在");
            }
            LambdaQueryWrapper<CardCCoach> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CardCCoach::getCardCId, cardCId)
                    .in(CardCCoach::getCoachId, cardCCoach.getCoachIds())
                    .eq(CardCCoach::getStoreId, SecurityUtils.getStore());
            cardCCoachService.remove(wrapper);
        }

        return UserConstants.DATABASE_SUCCESS;
    }

    @Override
    @Transactional
    public int removeCard(Long[] ids, Integer courseType)
    {
        for(Long id : ids){
            CardC cardC = this.selectCardCById(id);
            if (StringUtils.isNull(cardC) || cardC.getCourseType() != courseType) {
                throw new ServiceException("卡不存在");
            }
        }
        LambdaUpdateWrapper<CardC> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CardC::getStatus, CardConstants.CARD_STATUS_DELETED)
                .in(CardC::getId, ids)
                .eq(CardC::getStoreId, SecurityUtils.getStore());
        return this.baseMapper.update(updateWrapper);
    }

    @Override
    @Transactional
    public int updateContract(CardCVo cardCVo) {
        CardC cardC = cardCVo.getCardC();
        OrderCardSales orderCardSales = cardCVo.getOrderCardSalesVo().getOrderCardSales();
        List<OrderCardSalesPerformance> performanceList = cardCVo.getOrderCardSalesVo().getPerformanceList();

        CardC oldCardC = this.selectCardCById(cardC.getId());
        if(StringUtils.isNull(oldCardC)){
            throw new ServiceException("储值卡不存在");
        }
        if(!oldCardC.getStoreId().equals(SecurityUtils.getStore())){
            throw new ServiceException("非办卡门店无法编辑合同信息");
        }
        //开卡日期校验
        Integer status = cardC.getStatus();
        LocalDate effectTimeStart = cardC.getEffectTimeStart().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate now = LocalDate.now();
        if(status == CardConstants.CARD_STATUS_OPENED){
            if(!effectTimeStart.isBefore(now)){
                throw new ServiceException("开卡日期不能晚于当前日期");
            }
        }else if(status == CardConstants.CARD_STATUS_NOTOPENED) {
            if (!effectTimeStart.isAfter(now)) {
                throw new ServiceException("开卡日期必须在当前日期之后");
            }
        }else if(status == CardConstants.CARD_STATUS_EXPIRED) {
            Integer periodActual = oldCardC.getPeriodActual();
            LocalDate effectTimeEnd = effectTimeStart.plusDays(periodActual);
            if (!effectTimeEnd.isBefore(now)) {
                throw new ServiceException("有效截止日期必须在当前日期之前");
            }
        }
        //修改售卡订单信息
        if(oldCardC.getCardFrom() == CardConstants.ORDER_CARD){
            OrderCardSales order = orderCardSalesMapper.selectOne(new LambdaQueryWrapper<OrderCardSales>().eq(OrderCardSales::getId, oldCardC.getSourceOrderId()));
            if(StringUtils.isNull(order)){
                throw new ServiceException("售卡订单不存在");
            }
            orderCardSalesMapper.update(new LambdaUpdateWrapper<OrderCardSales>().eq(OrderCardSales::getId, order.getId())
                    .set(OrderCardSales::getPaymentMode, orderCardSales.getPaymentMode())
                    .set(OrderCardSales::getPaymentAmount, orderCardSales.getPaymentAmount())
                    .set(OrderCardSales::getSalesType, orderCardSales.getSalesType())
                    .set(OrderCardSales::getOrderTime, orderCardSales.getOrderTime())
                    .set(OrderCardSales::getEffectType, orderCardSales.getEffectType())
                    .set(OrderCardSales::getSpecifyDay, orderCardSales.getSpecifyDay())
                    .set(OrderCardSales::getEffectTimeStart, orderCardSales.getEffectTimeStart()));
            orderCardSalesPerformanceMapper.delete(new LambdaQueryWrapper<OrderCardSalesPerformance>()
                    .eq(OrderCardSalesPerformance::getOrderCardSalesId, order.getId()));
            if(StringUtils.isNotEmpty(performanceList)){
                for (OrderCardSalesPerformance performance : performanceList) {
                    performance.setId(null);
                    performance.setOrderCardSalesId(order.getId());
                    orderCardSalesPerformanceMapper.insert(performance);
                }
            }
            Integer orderType = translateOrderType(oldCardC.getCourseType());
            ChannelOrderPay channelOrderPay = channelOrderPayMapper.selectOne(new LambdaQueryWrapper<ChannelOrderPay>().eq(ChannelOrderPay::getOrderId, order.getId())
                    .eq(ChannelOrderPay::getOrderType, orderType));
            if(StringUtils.isNotNull(channelOrderPay) && channelOrderPay.getPayScene() == PayConstants.PAY_SCENE_OFFLINE){
                //线下支付订单更新支付信息
                channelOrderPayMapper.update(new LambdaUpdateWrapper<ChannelOrderPay>().eq(ChannelOrderPay::getOrderId, order.getId())
                        .eq(ChannelOrderPay::getOrderType, orderType)
                        .set(ChannelOrderPay::getPayMode, orderCardSales.getPaymentMode())
                        .set(ChannelOrderPay::getPayFee, orderCardSales.getPaymentAmount()));
            }
        }
        //修改会籍卡信息
        LambdaUpdateWrapper<CardC> updateWrapper = new LambdaUpdateWrapper<CardC>().eq(CardC::getId, cardC.getId())
                .set(CardC::getCardNumber, cardC.getCardNumber())
                .set(CardC::getEffectTimeStart, cardC.getEffectTimeStart());
        this.baseMapper.update(updateWrapper);
        return UserConstants.DATABASE_SUCCESS;
    }

    private Integer translateOrderType(Integer courseType) {
        if(courseType == CourseConstants.COURSE_TYPE_PC){
            return OrderConstants.PAY_ORDER_TYPE_CARD_SALES_PC;
        }else if(courseType == CourseConstants.COURSE_TYPE_SC) {
            return OrderConstants.PAY_ORDER_TYPE_CARD_SALES_SC;
        }else if(courseType == CourseConstants.COURSE_TYPE_GC){
            return OrderConstants.PAY_ORDER_TYPE_CARD_SALES_GC;
        }else if(courseType == CourseConstants.COURSE_TYPE_TC){
            return OrderConstants.PAY_ORDER_TYPE_CARD_SALES_TC;
        }
        return null;
    }
}
