
# Spring
spring:
  mvc:
    pathmatch:
      # 解决高版本SpringBoot与Swagger不兼容问题
      matching-strategy: ant_path_matcher
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ************:8848
        group: czc
  #      config:
#        # 配置中心地址
#        server-addr: 127.0.0.1:8848
#        # 配置文件格式
#        file-extension: yml
#        # 共享配置
#        shared-configs:
#          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  redis:
    host: ************
    port: 6379
    password: Qwe123!@#
    database: 0
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 10
        maxWait: 60000
        connectTimeout: 30000
        socketTimeout: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        keepAlive: true
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        # 主库数据源
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***************************************************************************************************************************************************
          username: root
          password: Qwe123!@#
          # 从库数据源
#        db1:
#          driver-class-name: com.mysql.cj.jdbc.Driver
#          url: ********************************************************************************************************************************************************
#          username: root
#          password: Qwe123!@#

# mybatis配置
#mybatis:
#  # 搜索指定包别名
#  typeAliasesPackage: com.bf.system
#  # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  mapperLocations: classpath:mapper/**/*.xml

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.bf.*
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
  # 是否数据过滤
  dataFilter: true

# swagger配置
swagger:
  title: 系统模块接口文档
  license: Powered By ruoyi


# 日志配置
logging:
  level:
    com.bf: debug
    org.springframework: warn

# 数据源切换类型
dynamic-dataSource:
  type: user

# 支付渠道
channel:
  baseUrl:
  terminalIp: ***************
