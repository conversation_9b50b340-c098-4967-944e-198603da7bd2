package com.bf.card.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bf.card.domain.CardC;
import com.bf.card.domain.CardCCoach;
import com.bf.card.domain.vo.CardCVo;

import java.util.List;

/**
 * 卡_课程卡Service接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
public interface ICardCService extends IService<CardC>
{
    /**
     * 查询课程卡列表
     * @param cardc
     * @return
     */
    List<CardC> selectCardList(CardC cardc);


    /**
     * 根据id查询课程卡
     *
     * @param id 卡_课程卡主键
     * @return 卡_课程卡
     */
     CardC selectCardCById(Long id);

    /**
     * 查询课程卡
     * @param cardC
     * @return
     */
    CardC selectCardC(CardC cardC);

    /**
     * 查询课程卡列表（会员详情）
     * @param cardC
     * @return
     */
    List<CardC> selectCardListMemberDetails(CardC cardC);

    /**
     * 查询课程卡（会员详情）
     * @param id
     * @return
     */
    CardC selectCardCByIdMemberDetails(Long id);

    /**
     * 根据订单id查询课程卡
     * @param orderId
     * @return
     */
    CardC selectCardCByOrderId(Long orderId);

    /**
     * 插入课程卡
     * @param cardCVo
     * @return
     */
     int insertCardC(CardCVo cardCVo);

    /**
     * 修改卡_课程卡
     * @param cardC 卡_课程卡
     * @return 结果
     */
     int updateCardC(CardC cardC);

    /**
     * 构建课程卡vo
     * @param cardCVo
     * @return
     */
    CardCVo buildCardCVo(CardCVo cardCVo);

    /**
     * 扣卡（次卡）-返回对应扣卡次数、价值等
     * @param memberId
     * @param deductCardId
     * @param deductCardTimesTotal
     * @return
     */
    CardC deductCard(Long memberId, Long deductCardId, Integer deductCardTimesTotal);

    /**
     * 扣卡（次卡）-返回对应扣卡次数、价值等
     * @param memberId
     * @param deductCardId
     * @param deductCardTimesTotal
     * @param needLock 是否需要锁定卡内次数，如预约课程时需要锁定次数，签课后才真正扣卡，如果取消预约则退回次数
     * @return
     */
    CardC deductCard(Long memberId, Long deductCardId, Integer deductCardTimesTotal, Boolean needLock);

    /**
     * 扣卡（次卡）- 将锁定次数转为卡消耗次数
     * @param timesLock
     * @param timesGiftLock
     * @return
     */
    void deductCard(Long deductCardId,Integer timesLock, Integer timesGiftLock);

    /**
     * 查询会员拥有的课程卡（包括共享）
     * @param cardC
     * @return
     */
    CardC selectMemberCardC(CardC cardC);

    /**
     * 返还卡内次数（次卡）
     * @param cardId
     * @param cardTimes
     * @param cardTimesGift
     * @return
     */
    CardC returnCard(Long cardId, Integer cardTimes, Integer cardTimesGift);

    /**
     * 返还卡内次数（次卡）
     * @param cardId
     * @param cardTimes
     * @param cardTimesGift
     * @param returnLock 是否返还锁定次数
     * @return
     */
    CardC returnCard(Long cardId, Integer cardTimes, Integer cardTimesGift, Boolean returnLock);
    
    /**
     * 获取可用卡
     * @param cardC
     * @return
     */
    List<CardC> listAvailableCard(CardC cardC);

    /**
     * 分配上课教练
     * @param cardCCoach
     * @param courseType
     * @return
     */
    boolean assignCoach(CardCCoach cardCCoach, Integer courseType);

    /**
     * 移除上课教练
     * @param cardCCoach
     * @param courseType
     * @return
     */
    int removeCoach(CardCCoach cardCCoach, Integer courseType);

    /**
     * 删除课程卡
     * @param ids
     * @param courseType
     * @return
     */
    int removeCard(Long[] ids, Integer courseType);

    /**
     * 编辑合同信息
     */
    int updateContract(CardCVo cardCVo);
}
