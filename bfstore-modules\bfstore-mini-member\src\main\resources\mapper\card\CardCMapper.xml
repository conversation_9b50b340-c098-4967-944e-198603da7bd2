<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bf.card.mapper.CardCMapper">

    <resultMap type="CardC" id="CardCResult">
        <result property="id"    column="id"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="storeId"    column="store_id"    />
        <result property="isValid"    column="is_valid"    />
        <result property="cardTemplateCId"    column="card_template_c_id"    />
        <result property="name"    column="name"    />
        <result property="cardNumber"    column="card_number"    />
        <result property="category"    column="category"    />
        <result property="isExperience"    column="is_experience"    />
        <result property="status"    column="status"    />
        <result property="courseType"    column="course_type"    />
        <result property="baseSportId"    column="base_sport_id"    />
        <result property="usageStoreType"    column="usage_store_type"    />
        <result property="shareUpper"    column="share_upper"    />
        <result property="bookingUpper"    column="booking_upper"    />
        <result property="periodInit"    column="period_init"    />
        <result property="periodActual"    column="period_actual"    />
        <result property="timesInit"    column="times_init"    />
        <result property="timesActual"    column="times_actual"    />
        <result property="timesConsume"    column="times_consume"    />
        <result property="priceInit"    column="price_init"    />
        <result property="priceActual"    column="price_actual"    />
        <result property="salesStore"    column="sales_store"    />
        <result property="cardFrom"    column="card_from"    />
        <result property="sourceOrderId"    column="source_order_id"    />
        <result property="applyType"    column="apply_type"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="effectType"    column="effect_type"    />
        <result property="specifyDay"    column="specify_day"    />
        <result property="effectTimeStart"    column="effect_time_start"    />
        <result property="salesChannel"    column="sales_channel"    />
        <result property="orderNum"    column="order_num"    />
        <result property="frontCover"    column="front_cover"    />
        <result property="introduce"    column="introduce"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="signNumber"  column="sign_number" />
        <result property="baseSportName"  column="base_sport_name" />
        <result property="salesUserName"    column="sales_user_name"    />
        <result property="salesUserPhone"    column="sales_user_phone"    />
        <result property="memberId"    column="member_id"    />
        <result property="memberName"    column="member_name"    />
        <result property="memberPhone"    column="member_phone"    />
        <result property="memberSex"    column="member_sex"    />
        <result property="residualValue"    column="residual_value"    />
        <result property="residualAmount"    column="residual_amount"    />
        <result property="residualPeriod"    column="residual_period"    />
        <result property="residualTimes"    column="residual_times"    />
        <result property="effectTimeEnd"    column="effect_time_end"    />
        <result property="lastSignTime"    column="last_sign_time"    />
        <result property="orderFrom"    column="order_from"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="timesDeduct"    column="times_deduct"    />
        <result property="storeName" column="store_name" />
        <result property="isBind" column="is_bind" />
        <result property="trainingClassName" column="training_class_name" />
        <association property="course">
            <result property="timesDeduct"    column="times_deduct"    />
        </association>
    </resultMap>

    <sql id="selectCardCVo">
        SELECT
            id,tenant_id,store_id,is_valid,card_template_c_id,name,sales_user_id,card_number,category,is_experience,status,
            course_type,base_sport_id,usage_store_type,share_upper,
            booking_upper,period_init,period_actual,times_init,times_actual,times_consume,price_init,price_actual,sales_store,card_from,source_order_id,
            apply_type,apply_time,effect_type,specify_day,effect_time_start,sales_channel,order_num,front_cover,introduce,last_sign_time,
            sign_number,remark,create_time,create_by,
            update_time,update_by
        FROM
            card_c
    </sql>

    <sql id="BaseColumn">
        cc.id,cc.tenant_id,cc.store_id,cc.is_valid,cc.card_template_c_id,cc.name,cc.suffix,cc.card_number,cc.card_number,cc.category,cc.is_experience,
        cc.status,cc.course_type,cc.base_sport_id,cc.share_upper,cc.booking_upper,cc.period_init,cc.period_actual,cc.times_init,
        cc.times_actual,cc.times_consume,cc.price_init,cc.price_actual,cc.sales_store,cc.card_from,cc.source_order_id,cc.apply_type,cc.apply_time,
        cc.effect_type,cc.specify_day,cc.effect_time_start,cc.sales_channel,cc.order_num,cc.front_cover,cc.introduce,
        cc.remark,cc.create_time,cc.create_by,cc.update_time,cc.update_by,cc.sign_number,cc.last_sign_time,
        f.url as avatar_url,
        s.name as store_name,
        bs.name as base_sport_name,
        <!--剩余价值计算-->
        CASE
        WHEN cc.category = 0 THEN IFNULL(((cc.period_actual  - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start), 0))/ cc.period_actual),0) * cc.price_actual
        WHEN cc.category = 1 THEN IFNULL(((cc.times_actual  - IFNULL(cc.times_consume, 0))/ cc.times_actual ),0) * cc.price_actual
        ELSE 0
        END AS residual_value,
        <!--剩余数量计算-->
        CASE
        WHEN cc.category = 0 THEN (cc.period_actual  - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0))
        WHEN cc.category = 1 THEN (cc.times_actual - IFNULL(cc.times_consume,0))
        ELSE 0
        END AS residual_amount,
        <!--有效期至-->
        DATE_ADD( cc.effect_time_start, INTERVAL  cc.period_actual  DAY ) AS effect_time_end,
        IF(cc.suffix IS NOT NULL AND cc.suffix != '', CONCAT(cc.name, '(', cc.suffix, ')'), cc.name) AS name_suffix,
    </sql>

    <select id="selectCardCById"  resultMap="CardCResult">
        select <include refid="BaseColumn"></include>
        <!--剩余有效期-->
        cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0) as residual_period,
        <!--剩余次数-->
        cc.times_actual - IFNULL(times_consume,0) as residual_times,
        m.id as member_id,m.name as member_name,m.phonenumber as member_phone, m.sex as member_sex,
        su.phonenumber as sales_user_phone,su.nick_name as sales_user_name,
        ru.store_id as rel_store_id,
        f.url as avatar_url
        from card_c cc
        left join card_c_rel_store_usage ru on cc.id = ru.card_c_id
        left join tenant_store s on cc.store_id = s.id
        left join card_rel_member crm on cc.id = crm.card_id
        left join member m on crm.member_id = m.id
        left join sys_user su on su.user_id = cc.sales_user_id
        left join sys_file f on m.avatar = f.id
        left join base_sport bs on cc.base_sport_id = bs.id
        where cc.id = #{id}
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectCardList" resultMap="CardCResult">
        SELECT
        <include refid="BaseColumn"></include>
        m.id AS member_id,m.name AS member_name,m.phonenumber AS member_phone, m.sex as member_sex,
        su.phonenumber AS sales_user_phone,su.name AS sales_user_name,
        ru.store_id AS rel_store_id
        FROM
        card_c cc
        LEFT JOIN card_c_rel_store_usage ru ON cc.id = ru.card_c_id
        left join tenant_store s on cc.store_id = s.id
        LEFT JOIN card_rel_member crm ON cc.id = crm.card_id
        LEFT JOIN member m ON crm.member_id = m.id
        left join sys_file f ON m.avatar = f.id
        LEFT JOIN sys_user su ON su.user_id = cc.sales_user_id
        left join base_sport bs on cc.base_sport_id = bs.id
        where 1=1
        <if test="searchValue != null and searchValue != ''">
            and cc.name like concat('%', #{searchValue}, '%')
        </if>
        <if test="category != null ">and cc.category = #{category}</if><!--卡种-->
        <if test="isExperience != null ">and cc.is_experience = #{isExperience}</if><!--是否体验-->
        <if test="status != null and status == 20">
            and cc.status = 0
            and DATE_ADD( cc.effect_time_start, INTERVAL  cc.period_actual  DAY ) > CURDATE()
        </if>    <!--有效卡-->
        <if test="status != null and status != 20">and cc.status = #{status}</if><!--状态-->
        <if test="status != null and status != 20">and cc.status = #{status}</if><!--状态-->
        <if test="applyType != null ">and cc.apply_type = #{applyType}</if><!--办卡类型-->
        <if test="params.applyEndTime != null and params.applyEndTime != ''"><!--办卡结束时间-->
            AND date_format(cc.apply_time,'%y%m%d') &lt;= date_format(#{params.applyEndTime},'%y%m%d')
        </if>
        <if test="params.effectStartBeginTime != null and params.effectStartBeginTime != ''"><!-- 开卡开始时间检索 -->
            AND date_format(cc.effect_time_start,'%y%m%d') &gt;= date_format(#{params.effectStartBeginTime},'%y%m%d')
        </if>
        <if test="params.effectStartEndTime != null and params.effectStartEndTime != ''"><!-- 开卡结束时间检索 -->
            AND date_format(cc.effect_time_start,'%y%m%d') &lt;= date_format(#{params.effectStartEndTime},'%y%m%d')
        </if>
        <if test="params.effectBeginTime != null and params.effectBeginTime != ''"><!--有效期至开始时间-->
            AND date_format(DATE_ADD(cc.effect_time_start, INTERVAL (cc.period_actual) DAY),'%y%m%d')
            &gt;= date_format(#{params.effectBeginTime},'%y%m%d')
        </if>
        <if test="params.effectEndTime != null and params.effectEndTime != ''"><!--有效至结束时间-->
            AND date_format(DATE_ADD(cc.effect_time_start, INTERVAL (cc.period_actual) DAY),'%y%m%d')
            &lt;= date_format(#{params.effectEndTime},'%y%m%d')
        </if>
        <if test="params.lastSignBeginTime != null and params.lastSignBeginTime != ''"><!--最后签课开始时间-->
            AND date_format(cc.last_sign_time,'%y%m%d')
            &gt;= date_format(#{params.lastSignBeginTime},'%y%m%d')
        </if>
        <if test="params.lastSignEndTime != null and params.lastSignEndTime != ''"><!--最后签课结束时间-->
            AND date_format(cc.last_sign_time,'%y%m%d')
            &lt;= date_format(#{params.lastSignEndTime},'%y%m%d')
        </if>
        <if test="params.applyBeginTime != null and params.applyBeginTime != ''"><!--办卡时间-->
            AND date_format(cc.apply_time,'%y%m%d')
            &gt;= date_format(#{params.applyBeginTime},'%y%m%d')
        </if>
        <if test="params.applyEndTime != null and params.applyEndTime != ''"><!--办卡时间-->
            AND date_format(cc.apply_time,'%y%m%d')
            &lt;= date_format(#{params.applyEndTime},'%y%m%d')
        </if>
        <if test="courseType != null" >and cc.course_type = #{courseType}</if><!--课程类型-->
        <if test="isValid != null "> and cc.is_valid = #{isValid}</if><!--是否有效-->
        <if test="name != null ">and cc.name = #{name}</if><!--卡课名字-->
        <if test="id != null "> and cc.id = #{id}</if>
        <if test="salesUserId != null ">and cc.sales_user_id = #{salesUserId}</if>
        <if test="storeId != null and storeId !=''">and s.id = #{storeId} </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        order by cc.update_time desc
    </select>

    <select id="listAvailableCard" parameterType="CardC"  resultMap="CardCResult">
        select
        <include refid="BaseColumn"></include>
        <!--剩余有效期-->
        cc.period_actual - IFNULL(DATEDIFF(CURDATE(), cc.effect_time_start),0) as residual_period,
        <!--剩余次数-->
        cc.times_actual - IFNULL(times_consume,0) as residual_times,
        m.id as member_id,m.name as member_name,m.phonenumber as member_phone, m.sex as member_sex,
        su.phonenumber as sales_user_phone,su.nick_name as sales_user_name,
        ru.store_id as rel_store_id,
        f.url as avatar_url,
        bs.name as base_sport_name,
        c.times_deduct
        <if test="trainingClassFlag != null and trainingClassFlag == 1">
            ,case when cs.card_id is null then 0
            else 1
            end as is_bind,
            tc.name as training_class_name
        </if>
        from card_c cc
        left join card_c_rel_store_usage ru on cc.id = ru.card_c_id
        left join tenant_store s on ru.store_id = s.id
        left join card_rel_member crm1 on cc.id = crm1.card_id
        left join card_rel_member crm2 on cc.id = crm2.card_id
        left join member m on crm2.member_id = m.id
        left join sys_user su on su.user_id = cc.sales_user_id
        left join sys_file f on m.avatar = f.id
        left join card_template_c_course c on c.card_template_c_id = cc.card_template_c_id
        left join card_c_coach co on co.card_c_id = cc.id
        left join base_sport bs on bs.id = cc.base_sport_id
        <if test="trainingClassFlag != null and trainingClassFlag == 1">
            left join training_class_student cs on cs.card_id = cc.id
            left join training_class tc on tc.id = cs.training_class_id
        </if>
        where cc.is_valid = 1 and
        ((DATE_ADD(cc.effect_time_start, INTERVAL cc.period_actual DAY) > CURDATE())
        or (cc.effect_type = 3 and cc.effect_time_start is null))
        <if test="memberId != null">
            and crm1.member_id = #{memberId}
        </if>
        <if test="ignoreMemberIds != null">
            and crm1.member_id not in
            <foreach collection="ignoreMemberIds" item="memberId" open="(" separator="," close=")">
                #{memberId}
            </foreach>
        </if>
        and c.course_id = #{courseId}
        and cc.course_type = #{courseType} and crm2.is_owner = 1 and cc.status = 0
        <if test="coachId != null">
            and co.coach_id = #{coachId}
        </if>
        <if test="coachIds != null">
            and co.coach_id in
            <foreach collection="coachIds" item="coachId" open="(" separator="," close=")">
                #{coachId}
            </foreach>
        </if>
        <if test="searchValue != null and searchValue != ''"><!-- 关键字检索 -->
            and ( cc.name like concat('%', #{searchValue}, '%') )
        </if>
        <if test="id != null">
            and cc.id = #{id}
        </if>
        <if test="trainingClassFlag != null and trainingClassFlag == 1">
            and crm1.is_owner = 1
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
        <if test="trainingClassFlag != null and trainingClassFlag == 1">
            order by case
            when cs.card_id is null then 0
            else 1
            end
        </if>
    </select>

    <update id="deductCard" parameterType="CardC">
        update card_c
        set times_consume = times_consume + #{timesConsume}
        where id = #{id} and times_actual &gt;= times_consume + #{timesConsume}
    </update>

    <update id="returnCard">
        update card_c
        set times_consume = times_consume - #{timesConsume}
        where id = #{id} and times_consume &gt;= #{timesConsume}
    </update>
    
</mapper>
